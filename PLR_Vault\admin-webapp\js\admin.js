// Admin Panel JavaScript - VERSION 2.1
console.log("🚀 Loading Admin Panel v2.1 - " + new Date().toISOString());

import { auth, database, storage } from "./firebase-config.js";
import {
  signInWithEmailAndPassword,
  signInWithPopup,
  GoogleAuthProvider,
  signOut,
  onAuthStateChanged,
} from "https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js";
import {
  ref,
  get,
  set,
  push,
  remove,
  update,
  onValue,
  off,
  child,
} from "https://www.gstatic.com/firebasejs/10.7.1/firebase-database.js";
import {
  ref as storageRef,
  uploadBytesResumable,
  getDownloadURL,
  deleteObject,
} from "https://www.gstatic.com/firebasejs/10.7.1/firebase-storage.js";

// Global variables
let currentUser = null;
let currentUserRole = null;

// Categories list
const CATEGORIES = [
  "Business & Entrepreneurship",
  "Health & Wellness",
  "Technology & Innovation",
  "Recipes & Nutrition",
  "MRR Video Courses",
  "Finance & Investment",
  "Self-Improvement & Motivation",
  "Marketing & Branding",
  "Design & Templates",
  "Spirituality & Mindfulness",
  "Career & Freelancing",
  "AI & Automation",
  "Education & eLearning",
  "Legal & Business Docs",
  "eCommerce & Dropshipping",
  "Parenting & Family",
  "Fashion & Beauty",
  "Travel & Lifestyle",
  "Kids & Learning",
  "Entertainment & Fun",
];

// Initialize the application
document.addEventListener("DOMContentLoaded", function () {
  initializeApp();
});

function initializeApp() {
  console.log("🚀 Initializing Admin App...");

  // Check authentication state
  onAuthStateChanged(auth, async (user) => {
    console.log("🔄 Auth state changed:", user ? user.email : "No user");

    if (user) {
      console.log("✅ User authenticated:", user.email, "UID:", user.uid);
      await checkUserRole(user);
    } else {
      console.log("❌ No authenticated user, showing login form");
      showLoginForm();
    }
  });

  // Setup login form
  document.getElementById("loginForm").addEventListener("submit", handleLogin);

  // Populate category dropdowns
  populateCategoryDropdowns();
}

async function checkUserRole(user) {
  try {
    console.log("🔍 === CHECKING USER ROLE v2.3 ===");
    console.log("📧 User email:", user.email);
    console.log("🆔 Auth UID:", user.uid);

    // Skip UID lookup and go directly to email-based search
    // This avoids the "Invalid token in path" error
    console.log("� Finding user by email (primary method)...");
    const usersRef = ref(database, "users");
    console.log("📡 Fetching all users from database...");
    const allUsersSnapshot = await get(usersRef);
    console.log("📊 All users snapshot exists:", allUsersSnapshot.exists());

    if (allUsersSnapshot.exists()) {
      const allUsers = allUsersSnapshot.val();
      console.log("👥 Total users in database:", Object.keys(allUsers).length);
      console.log("🔍 All user UIDs:", Object.keys(allUsers));

      let foundUser = null;
      let foundUID = null;

      for (const [uid, userData] of Object.entries(allUsers)) {
        console.log(`🔍 Checking UID ${uid}:`, userData?.email);
        if (
          userData &&
          userData.email &&
          userData.email.toLowerCase() === user.email.toLowerCase()
        ) {
          foundUser = userData;
          foundUID = uid;
          console.log("✅ Found user by email with UID:", uid);
          console.log("📊 User data:", userData);
          break;
        }
      }

      if (foundUser) {
        const role = foundUser.role;
        console.log("🎭 Found user role:", role);

        if (role === "super_admin") {
          console.log("👑 SUPER ADMIN ACCESS GRANTED!");
          currentUser = user;
          currentUserRole = "super_admin";
          showAdminDashboard();
        } else if (role === "admin") {
          console.log("🔧 ADMIN ACCESS GRANTED!");
          currentUser = user;
          currentUserRole = "admin";
          showAdminDashboard();
        } else if (role === "user") {
          console.log("❌ ACCESS DENIED - User role detected");
          showError(
            "Access denied. You are a regular user. Only admins and super admins can access this panel."
          );
          await signOut(auth);
        } else {
          console.log("❌ ACCESS DENIED - Unknown role:", role);
          showError(
            `Access denied. Unknown role: "${role}". Please contact the super admin.`
          );
          await signOut(auth);
        }
      } else {
        console.log("❌ No user found with email:", user.email);
        showError(
          "User not found in database. Please ensure you have signed up using the mobile app first."
        );
        await signOut(auth);
      }
    } else {
      console.log("❌ No users found in database at all");
      showError(
        "Database error. No users found. Please contact the super admin."
      );
      await signOut(auth);
    }
  } catch (error) {
    console.error("💥 ERROR checking user role:", error);
    console.error("📝 Error details:", error.message);
    console.error("📚 Error stack:", error.stack);
    console.error("🔧 Error name:", error.name);
    console.error("🔧 Error code:", error.code);
    showError(
      `Error verifying user permissions: ${error.message}. Please try again.`
    );
    await signOut(auth);
  }
}

function showLoginForm() {
  document.getElementById("loginContainer").style.display = "block";
  document.getElementById("adminDashboard").style.display = "none";
  document.getElementById("userDropdown").style.display = "none";
}

function showAdminDashboard() {
  document.getElementById("loginContainer").style.display = "none";
  document.getElementById("adminDashboard").style.display = "block";
  document.getElementById("userDropdown").style.display = "block";
  document.getElementById("userEmail").textContent = currentUser.email;

  console.log("Setting up dashboard for role:", currentUserRole);

  // Show/hide sections based on role
  if (currentUserRole === "super_admin") {
    // Super Admin: Can manage users, admins, and content
    document.getElementById("usersLink").style.display = "block";
    document.getElementById("adminsLink").style.display = "block";
    console.log("Super admin access granted - showing all sections");
  } else if (currentUserRole === "admin") {
    // Admin: Can only manage content
    document.getElementById("usersLink").style.display = "none";
    document.getElementById("adminsLink").style.display = "none";
    console.log("Admin access granted - hiding user/admin management");
  } else {
    // Fallback: Hide all admin sections
    document.getElementById("usersLink").style.display = "none";
    document.getElementById("adminsLink").style.display = "none";
    console.log("Limited access - hiding admin sections");
  }

  // Load dashboard data
  loadDashboardData();
  showSection("dashboard");
}

// Check if user exists in Realtime Database
async function checkUserExistsInDatabase(email) {
  try {
    console.log("=== CHECKING USER EXISTS IN DATABASE ===");
    console.log("Looking for email:", email);

    const usersRef = ref(database, "users");
    const snapshot = await get(usersRef);

    if (snapshot.exists()) {
      const users = snapshot.val();
      console.log("Database users found:", Object.keys(users).length);
      console.log("Database user UIDs:", Object.keys(users));

      // Check if any user has this email
      for (const [uid, userData] of Object.entries(users)) {
        console.log(`Checking UID ${uid}:`, userData.email);
        if (
          userData &&
          userData.email &&
          userData.email.toLowerCase() === email.toLowerCase()
        ) {
          console.log("✅ USER FOUND in database:", userData);
          return true;
        }
      }
    }
    console.log("❌ USER NOT FOUND in database:", email);
    return false;
  } catch (error) {
    console.error("❌ ERROR checking user in database:", error);
    return false;
  }
}

async function handleLogin(e) {
  e.preventDefault();

  const email = document.getElementById("email").value;
  const password = document.getElementById("password").value;
  const errorDiv = document.getElementById("loginError");

  try {
    errorDiv.style.display = "none";

    // First check if user exists in our database
    const userExists = await checkUserExistsInDatabase(email);
    if (!userExists) {
      showError(
        "Account not found in our system. Please sign up first using the mobile app, then try logging in again."
      );
      return;
    }

    // If user exists in database, proceed with Firebase Authentication
    await signInWithEmailAndPassword(auth, email, password);
  } catch (error) {
    console.error("Login error:", error);
    let errorMessage = "Login failed. Please try again.";

    switch (error.code) {
      case "auth/user-not-found":
        errorMessage =
          "No account found with this email address. Please sign up first using the mobile app.";
        break;
      case "auth/wrong-password":
        errorMessage = "Incorrect password. Please try again.";
        break;
      case "auth/invalid-email":
        errorMessage = "Invalid email address.";
        break;
      case "auth/too-many-requests":
        errorMessage = "Too many failed attempts. Please try again later.";
        break;
      case "auth/invalid-credential":
        errorMessage =
          "Invalid email or password. Please check your credentials or sign up first using the mobile app.";
        break;
    }

    showError(errorMessage);
  }
}

// Google Sign-In function
async function signInWithGoogle() {
  try {
    console.log("🚀 Starting Google sign-in...");

    const provider = new GoogleAuthProvider();
    provider.setCustomParameters({
      prompt: "select_account",
    });

    // Sign in with Google
    const result = await signInWithPopup(auth, provider);
    const user = result.user;

    console.log("✅ Google sign-in successful!");
    console.log("📧 User email:", user.email);
    console.log("🆔 User UID:", user.uid);
    console.log("🔄 Authentication will be handled by onAuthStateChanged...");

    // The onAuthStateChanged listener will automatically trigger
    // and call checkUserRole(user) to verify permissions
  } catch (error) {
    console.error("💥 Google sign-in error:", error);
    console.error("📝 Error code:", error.code);
    console.error("📝 Error message:", error.message);

    let errorMessage = "Google sign-in failed. Please try again.";

    switch (error.code) {
      case "auth/popup-closed-by-user":
        errorMessage = "Sign-in was cancelled. Please try again.";
        break;
      case "auth/popup-blocked":
        errorMessage =
          "Pop-up was blocked by your browser. Please allow pop-ups and try again.";
        break;
      case "auth/cancelled-popup-request":
        errorMessage = "Sign-in was cancelled. Please try again.";
        break;
      case "auth/network-request-failed":
        errorMessage =
          "Network error. Please check your internet connection and try again.";
        break;
      default:
        errorMessage = `Google sign-in failed: ${error.message}. Please try again or use email login.`;
    }

    showError(errorMessage);
  }
}

// Make signInWithGoogle globally available
window.signInWithGoogle = signInWithGoogle;

function showError(message) {
  const errorDiv = document.getElementById("loginError");
  errorDiv.textContent = message;
  errorDiv.style.display = "block";
}

async function logout() {
  try {
    await signOut(auth);
    showLoginForm();
  } catch (error) {
    console.error("Logout error:", error);
  }
}

function showSection(sectionName) {
  console.log(
    `Attempting to show section: ${sectionName}, User role: ${currentUserRole}`
  );

  // Check role-based access before showing sections
  if (sectionName === "users" && currentUserRole !== "super_admin") {
    console.log("Access denied to users section - not super admin");
    alert("Access denied. Only super admins can manage users.");
    return;
  }

  if (sectionName === "admins" && currentUserRole !== "super_admin") {
    console.log("Access denied to admins section - not super admin");
    alert("Access denied. Only super admins can manage admins.");
    return;
  }

  // Hide all sections
  const sections = document.querySelectorAll(".content-section");
  sections.forEach((section) => (section.style.display = "none"));

  // Remove active class from all nav items
  const navItems = document.querySelectorAll(".list-group-item");
  navItems.forEach((item) => item.classList.remove("active"));

  // Show selected section
  document.getElementById(sectionName + "Section").style.display = "block";

  // Add active class to nav item - SIMPLIFIED VERSION
  try {
    // Try to find the nav item and make it active
    const navItems = document.querySelectorAll(".list-group-item");
    navItems.forEach((item) => {
      if (
        item.textContent.toLowerCase().includes(sectionName.toLowerCase()) ||
        item.getAttribute("onclick") === `showSection('${sectionName}')`
      ) {
        item.classList.add("active");
      }
    });
  } catch (navError) {
    console.log("Nav activation error (non-critical):", navError);
    // Continue anyway - this is not critical for functionality
  }

  console.log(`Successfully showing section: ${sectionName}`);

  // Load section data
  switch (sectionName) {
    case "dashboard":
      loadDashboardData();
      break;
    case "content":
      loadContentData();
      break;
    case "users":
      if (currentUserRole === "super_admin") {
        loadUsersData();
      } else {
        console.log("Blocked users data loading - insufficient permissions");
      }
      break;
    case "admins":
      if (currentUserRole === "super_admin") {
        loadAdminsData();
      } else {
        console.log("Blocked admins data loading - insufficient permissions");
      }
      break;
  }
}

async function loadDashboardData() {
  try {
    // Load content count (handle nested structure)
    const contentRef = ref(database, "content");
    const contentSnapshot = await get(contentRef);
    let contentCount = 0;

    if (contentSnapshot.exists()) {
      const content = contentSnapshot.val();

      // Count content items in nested structure
      Object.entries(content).forEach(([key, value]) => {
        if (value && typeof value === "object") {
          if (value.title && value.description) {
            // Direct content item (flat structure)
            contentCount++;
          } else {
            // Category structure - count items in this category
            Object.entries(value).forEach(([itemId, item]) => {
              if (item && typeof item === "object" && item.title) {
                contentCount++;
              }
            });
          }
        }
      });
    }

    document.getElementById("totalContent").textContent = contentCount;

    // Load users count
    const usersRef = ref(database, "users");
    const usersSnapshot = await get(usersRef);
    let userCount = 0;
    let adminCount = 0;

    if (usersSnapshot.exists()) {
      const users = usersSnapshot.val();
      Object.values(users).forEach((user) => {
        if (user.role === "admin" || user.role === "super_admin") {
          adminCount++;
        } else {
          userCount++;
        }
      });
    }

    document.getElementById("totalUsers").textContent = userCount;
    document.getElementById("totalAdmins").textContent = adminCount;
  } catch (error) {
    console.error("Error loading dashboard data:", error);
  }
}

function populateCategoryDropdowns() {
  const categorySelects = ["categoryFilter", "contentCategory"];

  categorySelects.forEach((selectId) => {
    const select = document.getElementById(selectId);
    if (select) {
      // Clear existing options (except first one for filter)
      if (selectId === "categoryFilter") {
        select.innerHTML = '<option value="">All Categories</option>';
      } else {
        select.innerHTML = '<option value="">Select Category</option>';
      }

      // Add category options
      CATEGORIES.forEach((category) => {
        const option = document.createElement("option");
        option.value = category;
        option.textContent = category;
        select.appendChild(option);
      });
    }
  });
}

// Content Management Functions
async function loadContentData() {
  try {
    console.log("🔄 Loading content data...");
    const contentRef = ref(database, "content");
    const snapshot = await get(contentRef);
    const tableBody = document.getElementById("contentTableBody");

    tableBody.innerHTML = "";

    if (snapshot.exists()) {
      const content = snapshot.val();
      console.log("📊 Content structure:", content);

      // Check if content is organized by category (nested structure)
      let allContentItems = [];

      Object.entries(content).forEach(([key, value]) => {
        if (value && typeof value === "object") {
          // Check if this is a category (contains nested items) or direct content item
          if (value.title && value.description) {
            // Direct content item (flat structure)
            allContentItems.push({ id: key, ...value });
          } else {
            // Category structure - iterate through items in this category
            Object.entries(value).forEach(([itemId, item]) => {
              if (item && typeof item === "object" && item.title) {
                allContentItems.push({
                  id: itemId,
                  category: key, // Use the parent key as category
                  ...item,
                });
              }
            });
          }
        }
      });

      console.log("📋 Total content items found:", allContentItems.length);
      console.log("📋 Content items:", allContentItems);

      // Create rows for all content items
      allContentItems.forEach((item) => {
        const row = createContentRow(item.id, item);
        tableBody.appendChild(row);
      });

      if (allContentItems.length === 0) {
        tableBody.innerHTML =
          '<tr><td colspan="6" class="text-center">No content found</td></tr>';
      }
    } else {
      console.log("❌ No content found in database");
      tableBody.innerHTML =
        '<tr><td colspan="6" class="text-center">No content found</td></tr>';
    }
  } catch (error) {
    console.error("💥 Error loading content:", error);
    const tableBody = document.getElementById("contentTableBody");
    tableBody.innerHTML =
      '<tr><td colspan="6" class="text-center text-danger">Error loading content</td></tr>';
  }
}

function createContentRow(key, item) {
  const row = document.createElement("tr");
  const fileSize = item.fileSize ? formatFileSize(item.fileSize) : "Unknown";
  const createdDate = item.createdAt
    ? new Date(item.createdAt).toLocaleDateString()
    : "Unknown";
  const fileIcon = getFileIcon(item.fileType);

  row.innerHTML = `
        <td>
            <i class="${fileIcon} file-icon"></i>
            ${item.title}
        </td>
        <td><span class="badge bg-primary">${item.category}</span></td>
        <td>${item.fileType || "Unknown"}</td>
        <td>${fileSize}</td>
        <td>${createdDate}</td>
        <td>
            <button class="btn btn-sm btn-warning" onclick="editContent('${key}')">
                <i class="fas fa-edit"></i>
            </button>
            <button class="btn btn-sm btn-danger" onclick="deleteContent('${key}')">
                <i class="fas fa-trash"></i>
            </button>
        </td>
    `;

  return row;
}

function getFileIcon(fileType) {
  if (!fileType) return "fas fa-file file-default";

  const type = fileType.toLowerCase();
  if (type.includes("pdf")) return "fas fa-file-pdf file-pdf";
  if (type.includes("doc") || type.includes("docx"))
    return "fas fa-file-word file-doc";
  if (type.includes("video") || type.includes("mp4"))
    return "fas fa-file-video file-video";
  if (type.includes("image") || type.includes("jpg") || type.includes("png"))
    return "fas fa-file-image file-image";
  if (type.includes("zip") || type.includes("rar"))
    return "fas fa-file-archive file-archive";
  return "fas fa-file file-default";
}

function formatFileSize(bytes) {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}

function showAddContentModal() {
  const modal = new bootstrap.Modal(document.getElementById("addContentModal"));
  modal.show();
}

async function addContent() {
  const title = document.getElementById("contentTitle").value;
  const description = document.getElementById("contentDescription").value;
  const category = document.getElementById("contentCategory").value;
  const fileInput = document.getElementById("contentFile");
  const file = fileInput.files[0];

  if (!title || !description || !category || !file) {
    alert("Please fill in all fields and select a file.");
    return;
  }

  try {
    // Show progress
    const progressDiv = document.getElementById("uploadProgress");
    const progressBar = progressDiv.querySelector(".progress-bar");
    progressDiv.style.display = "block";

    // Upload file to storage
    const fileName = `${Date.now()}_${file.name}`;
    const fileRef = storageRef(storage, `content/${category}/${fileName}`);
    const uploadTask = uploadBytesResumable(fileRef, file);

    uploadTask.on(
      "state_changed",
      (snapshot) => {
        const progress =
          (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
        progressBar.style.width = progress + "%";
      },
      (error) => {
        console.error("Upload error:", error);
        alert("Upload failed. Please try again.");
        progressDiv.style.display = "none";
      },
      async () => {
        try {
          const downloadURL = await getDownloadURL(uploadTask.snapshot.ref);

          // Save metadata to database
          const contentData = {
            title: title,
            description: description,
            category: category,
            fileName: fileName,
            originalFileName: file.name,
            fileType: file.type,
            fileSize: file.size,
            downloadURL: downloadURL,
            createdAt: new Date().toIso8601String(),
            createdBy: currentUser.email,
          };

          const contentRef = ref(database, "content");
          await push(contentRef, contentData);

          // Close modal and refresh content
          bootstrap.Modal.getInstance(
            document.getElementById("addContentModal")
          ).hide();
          document.getElementById("addContentForm").reset();
          progressDiv.style.display = "none";
          progressBar.style.width = "0%";

          loadContentData();
          loadDashboardData();

          alert("Content added successfully!");
        } catch (error) {
          console.error("Error saving content metadata:", error);
          alert("Error saving content. Please try again.");
        }
      }
    );
  } catch (error) {
    console.error("Error adding content:", error);
    alert("Error adding content. Please try again.");
  }
}

function filterContentByCategory() {
  const selectedCategory = document.getElementById("categoryFilter").value;
  const rows = document.querySelectorAll("#contentTableBody tr");

  rows.forEach((row) => {
    const categoryCell = row.querySelector("td:nth-child(2)");
    if (categoryCell) {
      const category = categoryCell.textContent.trim();
      if (!selectedCategory || category === selectedCategory) {
        row.style.display = "";
      } else {
        row.style.display = "none";
      }
    }
  });
}

async function editContent(contentId) {
  // Implementation for editing content
  alert("Edit functionality will be implemented in the next update.");
}

async function deleteContent(contentId) {
  if (!confirm("Are you sure you want to delete this content?")) {
    return;
  }

  try {
    console.log("🗑️ Deleting content with ID:", contentId);

    // First, find the content item in the nested structure
    const contentRef = ref(database, "content");
    const snapshot = await get(contentRef);

    if (!snapshot.exists()) {
      alert("Content not found.");
      return;
    }

    const content = snapshot.val();
    let contentData = null;
    let contentPath = null;

    // Search through the nested structure
    for (const [categoryKey, categoryValue] of Object.entries(content)) {
      if (categoryValue && typeof categoryValue === "object") {
        if (
          categoryValue.title &&
          categoryValue.description &&
          categoryKey === contentId
        ) {
          // Direct content item (flat structure)
          contentData = categoryValue;
          contentPath = `content/${contentId}`;
          break;
        } else {
          // Category structure - search within category
          for (const [itemId, item] of Object.entries(categoryValue)) {
            if (
              itemId === contentId &&
              item &&
              typeof item === "object" &&
              item.title
            ) {
              contentData = item;
              contentPath = `content/${categoryKey}/${itemId}`;
              break;
            }
          }
          if (contentData) break;
        }
      }
    }

    if (!contentData || !contentPath) {
      alert("Content not found in database.");
      return;
    }

    console.log("📍 Found content at path:", contentPath);
    console.log("📄 Content data:", contentData);

    try {
      // Delete file from storage if it exists
      if (contentData.fileName && contentData.category) {
        const fileRef = storageRef(
          storage,
          `content/${contentData.category}/${contentData.fileName}`
        );
        await deleteObject(fileRef);
        console.log("🗑️ File deleted from storage");
      }
    } catch (storageError) {
      console.warn(
        "⚠️ Could not delete file from storage (may not exist):",
        storageError
      );
      // Continue with database deletion even if storage deletion fails
    }

    // Delete from database
    const itemRef = ref(database, contentPath);
    await remove(itemRef);
    console.log("🗑️ Content deleted from database");

    loadContentData();
    loadDashboardData();

    alert("Content deleted successfully!");
  } catch (error) {
    console.error("💥 Error deleting content:", error);
    alert("Error deleting content. Please try again.");
  }
}

// User Management Functions
async function loadUsersData() {
  try {
    const usersRef = ref(database, "users");
    const snapshot = await get(usersRef);
    const tableBody = document.getElementById("usersTableBody");

    tableBody.innerHTML = "";

    if (snapshot.exists()) {
      const users = snapshot.val();
      Object.entries(users).forEach(([uid, user]) => {
        if (user.role === "user") {
          const row = createUserRow(uid, user);
          tableBody.appendChild(row);
        }
      });
    }
  } catch (error) {
    console.error("Error loading users:", error);
  }
}

function createUserRow(uid, user) {
  const row = document.createElement("tr");
  const createdDate = user.createdAt
    ? new Date(user.createdAt).toLocaleDateString()
    : "Unknown";
  const lastLogin = user.lastLoginAt
    ? new Date(user.lastLoginAt).toLocaleDateString()
    : "Never";

  row.innerHTML = `
        <td>${user.email}</td>
        <td>${user.displayName || "N/A"}</td>
        <td><span class="badge bg-success">${user.role}</span></td>
        <td><span class="badge bg-info">${
          user.signUpMethod || "email"
        }</span></td>
        <td>${createdDate}</td>
        <td>${lastLogin}</td>
        <td>
            <button class="btn btn-sm btn-warning" onclick="makeAdmin('${uid}')">
                <i class="fas fa-user-shield"></i> Make Admin
            </button>
            <button class="btn btn-sm btn-danger" onclick="deleteUser('${uid}')">
                <i class="fas fa-trash"></i>
            </button>
        </td>
    `;

  return row;
}

async function makeAdmin(uid) {
  // Check if user has super admin privileges
  if (currentUserRole !== "super_admin") {
    alert("Access denied. Only super admins can promote users to admin.");
    return;
  }

  try {
    // Get current user data to check their current role
    const userRef = ref(database, `users/${uid}`);
    const snapshot = await get(userRef);

    if (snapshot.exists()) {
      const userData = snapshot.val();

      if (userData.role === "admin") {
        alert("This user is already an admin.");
        return;
      }

      if (userData.role === "super_admin") {
        alert("This user is already a super admin.");
        return;
      }

      if (
        !confirm(
          `Are you sure you want to promote "${userData.email}" from "${userData.role}" to "admin"?`
        )
      ) {
        return;
      }

      // Update user role to admin
      await update(userRef, { role: "admin" });

      loadUsersData();
      loadAdminsData();
      loadDashboardData();

      alert(`User "${userData.email}" promoted to admin successfully!`);
    } else {
      alert("User not found in database.");
    }
  } catch (error) {
    console.error("Error making user admin:", error);
    alert("Error promoting user. Please try again.");
  }
}

async function deleteUser(uid) {
  // Check if user has super admin privileges
  if (currentUserRole !== "super_admin") {
    alert("Access denied. Only super admins can delete users.");
    return;
  }

  if (
    !confirm(
      "Are you sure you want to delete this user? This will remove them from both the database and authentication system. This action cannot be undone."
    )
  ) {
    return;
  }

  try {
    // Get user data first to check if it's the current user
    const userRef = ref(database, `users/${uid}`);
    const snapshot = await get(userRef);

    if (snapshot.exists()) {
      const userData = snapshot.val();

      // Prevent super admin from deleting themselves
      if (userData.email === currentUser.email) {
        alert("You cannot delete your own account.");
        return;
      }

      // Prevent deleting other super admins
      if (
        userData.role === "super_admin" &&
        userData.email !== currentUser.email
      ) {
        alert("You cannot delete other super admin accounts.");
        return;
      }
    }

    // Remove from Realtime Database
    await remove(userRef);

    // Note: We cannot delete users from Firebase Auth from client-side
    // This would require Firebase Admin SDK on the server-side
    // For now, we'll just remove from database and show a note

    loadUsersData();
    loadAdminsData();
    loadDashboardData();

    alert(
      "User removed from database successfully! Note: The user's authentication account still exists but they cannot access the admin panel."
    );
  } catch (error) {
    console.error("Error deleting user:", error);
    alert("Error deleting user. Please try again.");
  }
}

// Admin Management Functions
async function loadAdminsData() {
  try {
    const usersRef = ref(database, "users");
    const snapshot = await get(usersRef);
    const tableBody = document.getElementById("adminsTableBody");

    tableBody.innerHTML = "";

    if (snapshot.exists()) {
      const users = snapshot.val();
      Object.entries(users).forEach(([uid, user]) => {
        if (user.role === "admin" || user.role === "super_admin") {
          const row = createAdminRow(uid, user);
          tableBody.appendChild(row);
        }
      });
    }
  } catch (error) {
    console.error("Error loading admins:", error);
  }
}

function createAdminRow(uid, user) {
  const row = document.createElement("tr");
  const createdDate = user.createdAt
    ? new Date(user.createdAt).toLocaleDateString()
    : "Unknown";
  const lastLogin = user.lastLoginAt
    ? new Date(user.lastLoginAt).toLocaleDateString()
    : "Never";
  const isSuperAdmin = user.role === "super_admin";

  row.innerHTML = `
        <td>${user.email}</td>
        <td>${user.displayName || "N/A"}</td>
        <td>${createdDate}</td>
        <td>${lastLogin}</td>
        <td>
            ${
              !isSuperAdmin
                ? `
                <button class="btn btn-sm btn-danger" onclick="removeAdmin('${uid}')">
                    <i class="fas fa-user-minus"></i> Remove Admin
                </button>
            `
                : '<span class="badge bg-warning">Super Admin</span>'
            }
        </td>
    `;

  return row;
}

function showAddAdminModal() {
  // Check if user has super admin privileges
  if (currentUserRole !== "super_admin") {
    alert("Access denied. Only super admins can add new admins.");
    return;
  }

  const modal = new bootstrap.Modal(document.getElementById("addAdminModal"));
  modal.show();
}

async function addAdmin() {
  // Check if user has super admin privileges
  if (currentUserRole !== "super_admin") {
    alert("Access denied. Only super admins can add new admins.");
    return;
  }

  const email = document.getElementById("adminEmail").value.trim();

  if (!email) {
    alert("Please enter an email address.");
    return;
  }

  try {
    // Find user by email
    const usersRef = ref(database, "users");
    const snapshot = await get(usersRef);

    if (snapshot.exists()) {
      const users = snapshot.val();
      let userFound = false;

      for (const [uid, user] of Object.entries(users)) {
        if (user.email === email) {
          userFound = true;

          if (user.role === "admin") {
            alert(`"${email}" is already an admin.`);
            return;
          }

          if (user.role === "super_admin") {
            alert(`"${email}" is already a super admin.`);
            return;
          }

          // Confirm promotion
          if (
            !confirm(
              `Are you sure you want to promote "${email}" from "${user.role}" to "admin"?`
            )
          ) {
            return;
          }

          // Make user admin
          const userRef = ref(database, `users/${uid}`);
          await update(userRef, { role: "admin" });

          // Close modal and refresh
          bootstrap.Modal.getInstance(
            document.getElementById("addAdminModal")
          ).hide();
          document.getElementById("addAdminForm").reset();

          loadAdminsData();
          loadUsersData();
          loadDashboardData();

          alert(`"${email}" promoted to admin successfully!`);
          break;
        }
      }

      if (!userFound) {
        alert(
          `No user found with email "${email}". The user must be registered in the mobile app first.`
        );
      }
    } else {
      alert("No users found in the database.");
    }
  } catch (error) {
    console.error("Error adding admin:", error);
    alert("Error adding admin. Please try again.");
  }
}

async function removeAdmin(uid) {
  // Check if user has super admin privileges
  if (currentUserRole !== "super_admin") {
    alert("Access denied. Only super admins can remove admin privileges.");
    return;
  }

  try {
    // Get current user data to check their current role
    const userRef = ref(database, `users/${uid}`);
    const snapshot = await get(userRef);

    if (snapshot.exists()) {
      const userData = snapshot.val();

      // Prevent removing super admin privileges
      if (userData.role === "super_admin") {
        alert("You cannot remove super admin privileges.");
        return;
      }

      // Prevent removing admin privileges from current user
      if (userData.email === currentUser.email) {
        alert("You cannot remove your own admin privileges.");
        return;
      }

      if (userData.role !== "admin") {
        alert("This user is not an admin.");
        return;
      }

      if (
        !confirm(
          `Are you sure you want to remove admin privileges from "${userData.email}"? They will become a regular user.`
        )
      ) {
        return;
      }

      // Update user role to user
      await update(userRef, { role: "user" });

      loadAdminsData();
      loadUsersData();
      loadDashboardData();

      alert(
        `Admin privileges removed from "${userData.email}" successfully! They are now a regular user.`
      );
    } else {
      alert("User not found in database.");
    }
  } catch (error) {
    console.error("Error removing admin:", error);
    alert("Error removing admin. Please try again.");
  }
}

// Make functions globally available
window.showSection = showSection;
window.logout = logout;
window.showAddContentModal = showAddContentModal;
window.showAddAdminModal = showAddAdminModal;
window.addContent = addContent;
window.addAdmin = addAdmin;
window.filterContentByCategory = filterContentByCategory;
window.editContent = editContent;
window.deleteContent = deleteContent;
window.makeAdmin = makeAdmin;
window.removeAdmin = removeAdmin;
window.deleteUser = deleteUser;
