import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../services/auth_service.dart';
import '../services/permission_service.dart';
import 'category_content_screen.dart';
import 'my_downloads_screen.dart';
import 'admin_screen.dart';

class CategoriesScreen extends StatefulWidget {
  const CategoriesScreen({super.key});

  @override
  State<CategoriesScreen> createState() => _CategoriesScreenState();
}

class _CategoriesScreenState extends State<CategoriesScreen> {
  final AuthService _authService = AuthService();
  final RealtimeDatabaseService _dbService = RealtimeDatabaseService();
  final PermissionService _permissionService = PermissionService();
  bool _isLoading = true;
  List<String> _categories = [];
  String? _error;

  @override
  void initState() {
    super.initState();
    _requestPermissionsAndLoadCategories();
  }

  Future<void> _requestPermissionsAndLoadCategories() async {
    // Request storage permissions first
    await _requestStoragePermissions();
    // Then load categories
    await _loadCategories();
  }

  Future<void> _requestStoragePermissions() async {
    try {
      final bool hasPermissions = await _permissionService
          .hasStoragePermissions();

      if (!hasPermissions) {
        // Show permission dialog
        final bool shouldRequest = await _showPermissionDialog();

        if (shouldRequest) {
          final bool granted = await _permissionService
              .requestStoragePermissions();

          if (!granted) {
            // Show settings dialog if permission denied
            await _showPermissionDeniedDialog();
          }
        }
      }
    } catch (e) {
      // Handle permission error silently
    }
  }

  Future<bool> _showPermissionDialog() async {
    final String requirements = await _permissionService
        .getPermissionRequirements();

    if (!mounted) return false;

    return await showDialog<bool>(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return AlertDialog(
              title: const Text('Storage Permission Required'),
              content: Text(requirements),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: const Text('Skip'),
                ),
                ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(true),
                  child: const Text('Grant Permission'),
                ),
              ],
            );
          },
        ) ??
        false;
  }

  Future<void> _showPermissionDeniedDialog() async {
    if (!mounted) return;

    await showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Permission Denied'),
          content: const Text(
            'Storage permission is required to download PLR content. You can grant permission manually in Settings > Apps > PLR Content App > Permissions.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('OK'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _permissionService.openPermissionSettings();
              },
              child: const Text('Open Settings'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _loadCategories() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      // Load only categories that have content
      final categories = await _dbService.getCategoriesWithContent();

      setState(() {
        _categories = categories;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'Failed to load categories: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  Future<void> _signOut() async {
    // Show confirmation dialog
    final bool? shouldSignOut = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Sign Out'),
          content: const Text('Are you sure you want to sign out?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('Sign Out'),
            ),
          ],
        );
      },
    );

    if (shouldSignOut != true) return;

    try {
      // Force sign out - this MUST work
      await _authService.signOut();

      // Force navigate to login screen
      if (mounted) {
        Navigator.of(context).pushNamedAndRemoveUntil(
          '/login',
          (route) => false, // Remove all previous routes
        );
      }
    } catch (e) {
      // Even if there's an error, force navigate to login
      if (mounted) {
        Navigator.of(context).pushNamedAndRemoveUntil(
          '/login',
          (route) => false, // Remove all previous routes
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final User? user = _authService.currentUser;

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: GestureDetector(
          onLongPress: () {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const AdminScreen()),
            );
          },
          child: const Text('PLR Content'),
        ),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const MyDownloadsScreen(),
                ),
              );
            },
            icon: const Icon(Icons.lock_open),
            tooltip: 'Unlocked Content',
          ),
          IconButton(
            onPressed: _signOut,
            icon: const Icon(Icons.logout),
            tooltip: 'Sign Out',
          ),
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Welcome Section
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(24.0),
              decoration: BoxDecoration(
                color: Colors.blue,
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(24),
                  bottomRight: Radius.circular(24),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Welcome back,',
                    style: TextStyle(
                      color: Colors.white.withValues(alpha: 0.9),
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    user?.displayName ?? user?.email?.split('@')[0] ?? 'User',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Explore our PLR/MRR content library',
                    style: TextStyle(
                      color: Colors.white.withValues(alpha: 0.8),
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),

            // Content Section
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'Categories',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        IconButton(
                          onPressed: _loadCategories,
                          icon: const Icon(Icons.refresh),
                          tooltip: 'Refresh',
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    // Categories List
                    Expanded(child: _buildCategoriesContent()),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoriesContent() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'Error loading categories',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _error!,
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey[500]),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadCategories,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_categories.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.folder_open, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'No categories available',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Content will appear here once uploaded',
              style: TextStyle(color: Colors.grey[500]),
            ),
          ],
        ),
      );
    }

    return GridView.builder(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio:
            0.85, // Reduced to give more height for the enhanced cards
      ),
      itemCount: _categories.length,
      itemBuilder: (context, index) {
        final category = _categories[index];
        return _buildCategoryCard(category);
      },
    );
  }

  Widget _buildCategoryCard(String category) {
    // Get category icon and color
    final categoryInfo = _getCategoryInfo(category);

    return Card(
      elevation: 8,
      shadowColor: categoryInfo['color'].withValues(alpha: 0.3),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => CategoryContentScreen(category: category),
            ),
          );
        },
        borderRadius: BorderRadius.circular(20),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                categoryInfo['color'].withValues(alpha: 0.15),
                categoryInfo['color'].withValues(alpha: 0.05),
                Colors.white,
              ],
            ),
            border: Border.all(
              color: categoryInfo['color'].withValues(alpha: 0.2),
              width: 1,
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: categoryInfo['color'].withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  categoryInfo['icon'],
                  size: 28,
                  color: categoryInfo['color'],
                ),
              ),
              const SizedBox(height: 12),
              Text(
                category,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 13,
                  fontWeight: FontWeight.w700,
                  color: Colors.grey[800],
                  height: 1.2,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 6),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: categoryInfo['color'].withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  'Explore',
                  style: TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.w600,
                    color: categoryInfo['color'],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Map<String, dynamic> _getCategoryInfo(String category) {
    switch (category) {
      case 'Business & Entrepreneurship':
        return {'icon': Icons.business, 'color': Colors.blue};
      case 'Health & Wellness':
        return {'icon': Icons.favorite, 'color': Colors.red};
      case 'Technology & Innovation':
        return {'icon': Icons.computer, 'color': Colors.purple};
      case 'Recipes & Nutrition':
        return {'icon': Icons.restaurant, 'color': Colors.green};
      case 'MRR Video Courses':
        return {'icon': Icons.play_circle_filled, 'color': Colors.deepOrange};
      case 'Finance & Investment':
        return {'icon': Icons.attach_money, 'color': Colors.teal};
      case 'Self-Improvement & Motivation':
        return {'icon': Icons.psychology, 'color': Colors.amber};
      case 'Marketing & Branding':
        return {'icon': Icons.campaign, 'color': Colors.indigo};
      case 'Design & Templates':
        return {'icon': Icons.design_services, 'color': Colors.pink};
      case 'Spirituality & Mindfulness':
        return {'icon': Icons.self_improvement, 'color': Colors.deepPurple};
      case 'Career & Freelancing':
        return {'icon': Icons.work, 'color': Colors.brown};
      case 'AI & Automation':
        return {'icon': Icons.smart_toy, 'color': Colors.cyan};
      case 'Education & eLearning':
        return {'icon': Icons.school, 'color': Colors.lightBlue};
      case 'Legal & Business Docs':
        return {'icon': Icons.gavel, 'color': Colors.blueGrey};
      case 'eCommerce & Dropshipping':
        return {'icon': Icons.shopping_cart, 'color': Colors.orange};
      case 'Parenting & Family':
        return {'icon': Icons.family_restroom, 'color': Colors.lightGreen};
      case 'Fashion & Beauty':
        return {'icon': Icons.face, 'color': Colors.pinkAccent};
      case 'Travel & Lifestyle':
        return {'icon': Icons.flight, 'color': Colors.deepOrange};
      case 'Kids & Learning':
        return {'icon': Icons.child_care, 'color': Colors.yellow};
      case 'Entertainment & Fun':
        return {'icon': Icons.celebration, 'color': Colors.redAccent};
      default:
        return {'icon': Icons.folder, 'color': Colors.grey};
    }
  }
}
