import 'package:flutter/foundation.dart';

class Logger {
  static const String _tag = 'PLR_VAULT';

  static void debug(String message) {
    if (kDebugMode) {
      debugPrint('[$_tag] DEBUG: $message');
    }
  }

  static void info(String message) {
    if (kDebugMode) {
      debugPrint('[$_tag] INFO: $message');
    }
  }

  static void warning(String message) {
    if (kDebugMode) {
      debugPrint('[$_tag] WARNING: $message');
    }
  }

  static void error(String message, [Object? error, StackTrace? stackTrace]) {
    if (kDebugMode) {
      debugPrint('[$_tag] ERROR: $message');
      if (error != null) {
        debugPrint('[$_tag] ERROR Details: $error');
      }
      if (stackTrace != null) {
        debugPrint('[$_tag] Stack Trace: $stackTrace');
      }
    }
  }
}
