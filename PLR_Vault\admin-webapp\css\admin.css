/* Admin Panel Styles - Modern Design */
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap");

:root {
  --primary-color: #6366f1;
  --primary-dark: #4f46e5;
  --secondary-color: #8b5cf6;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --danger-color: #ef4444;
  --info-color: #06b6d4;
  --dark-color: #1f2937;
  --light-color: #f9fafb;
  --sidebar-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --card-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  --border-radius: 12px;
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

body {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI",
    sans-serif;
  font-weight: 400;
  line-height: 1.6;
  color: #374151;
  min-height: 100vh;
}

.sidebar {
  background: var(--sidebar-bg);
  min-height: calc(100vh - 56px);
  padding: 0;
  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
  position: relative;
}

.sidebar::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 100%
  );
  pointer-events: none;
}

.sidebar .list-group-item {
  background-color: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.8);
  border-radius: 0;
  padding: 18px 24px;
  transition: var(--transition);
  font-weight: 500;
  position: relative;
  overflow: hidden;
}

.sidebar .list-group-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: var(--transition);
}

.sidebar .list-group-item:hover::before {
  left: 100%;
}

.sidebar .list-group-item:hover {
  background: rgba(255, 255, 255, 0.15);
  color: #fff;
  transform: translateX(5px);
  backdrop-filter: blur(10px);
}

.sidebar .list-group-item.active {
  background: rgba(255, 255, 255, 0.2);
  color: #fff;
  border-left: 4px solid #fff;
  box-shadow: inset 0 0 20px rgba(255, 255, 255, 0.1);
}

.main-content {
  padding: 32px;
  background: transparent;
}

.content-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: var(--border-radius);
  padding: 40px;
  box-shadow: var(--card-shadow);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.content-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(
    90deg,
    var(--primary-color),
    var(--secondary-color)
  );
}

.content-section h2 {
  color: var(--dark-color);
  font-weight: 700;
  margin-bottom: 32px;
  position: relative;
  padding-bottom: 16px;
}

.content-section h2::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 3px;
  background: linear-gradient(
    90deg,
    var(--primary-color),
    var(--secondary-color)
  );
  border-radius: 2px;
}

.card {
  border: none;
  border-radius: var(--border-radius);
  box-shadow: var(--card-shadow);
  transition: var(--transition);
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  overflow: hidden;
  position: relative;
}

.card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(
    90deg,
    var(--primary-color),
    var(--secondary-color)
  );
  transform: scaleX(0);
  transition: var(--transition);
}

.card:hover::before {
  transform: scaleX(1);
}

.card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.card-body {
  padding: 24px;
}

.card.bg-primary {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-dark)
  ) !important;
  color: white;
}

.card.bg-success {
  background: linear-gradient(135deg, var(--success-color), #059669) !important;
  color: white;
}

.card.bg-warning {
  background: linear-gradient(135deg, var(--warning-color), #d97706) !important;
  color: white;
}

.card.bg-info {
  background: linear-gradient(135deg, var(--info-color), #0891b2) !important;
  color: white;
}

.table {
  border-radius: var(--border-radius);
  overflow: hidden;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  box-shadow: var(--card-shadow);
}

.table thead th {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-dark)
  );
  color: #fff;
  border: none;
  font-weight: 600;
  padding: 20px 16px;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.table tbody td {
  padding: 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  vertical-align: middle;
}

.table tbody tr {
  transition: var(--transition);
}

.table tbody tr:hover {
  background: rgba(99, 102, 241, 0.05);
  transform: scale(1.01);
}

.btn {
  border-radius: var(--border-radius);
  font-weight: 600;
  transition: var(--transition);
  padding: 12px 24px;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: none;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: var(--transition);
}

.btn:hover::before {
  left: 100%;
}

.btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.btn-primary {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-dark)
  );
  box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
}

.btn-success {
  background: linear-gradient(135deg, var(--success-color), #059669);
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.btn-warning {
  background: linear-gradient(135deg, var(--warning-color), #d97706);
  box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
}

.btn-danger {
  background: linear-gradient(135deg, var(--danger-color), #dc2626);
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

.btn-outline-danger {
  background: transparent;
  border: 2px solid #dc2626;
  color: #dc2626;
  font-weight: 600;
}

.btn-outline-danger:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  border-color: #dc2626;
  color: white;
  box-shadow: 0 8px 25px rgba(220, 38, 38, 0.3);
}

.btn-sm {
  padding: 8px 16px;
  font-size: 12px;
  margin: 0 4px;
}

.modal-content {
  border-radius: 20px;
  border: none;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.95);
}

.modal-header {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-dark)
  );
  color: #fff;
  border-radius: 20px 20px 0 0;
  padding: 24px;
  border: none;
}

.modal-header .modal-title {
  font-weight: 700;
  font-size: 18px;
}

.modal-body {
  padding: 32px;
}

.modal-footer {
  padding: 24px 32px;
  border: none;
}

.form-control,
.form-select {
  border-radius: var(--border-radius);
  border: 2px solid rgba(99, 102, 241, 0.1);
  transition: var(--transition);
  padding: 16px;
  font-size: 14px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
}

.form-control:focus,
.form-select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.15);
  background: rgba(255, 255, 255, 0.95);
  transform: translateY(-2px);
}

.form-label {
  font-weight: 600;
  color: var(--dark-color);
  margin-bottom: 8px;
  font-size: 14px;
}

.alert {
  border-radius: 10px;
  border: none;
}

.progress {
  border-radius: 20px;
  height: 12px;
  background: rgba(99, 102, 241, 0.1);
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.progress-bar {
  border-radius: 20px;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  position: relative;
  overflow: hidden;
}

.progress-bar::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .sidebar {
    min-height: auto;
  }

  .main-content {
    padding: 15px;
  }

  .content-section {
    padding: 20px;
  }
}

/* Loading Spinner */
.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

/* Status Badges */
.badge {
  font-size: 0.75em;
  padding: 8px 12px;
  border-radius: 20px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.badge.bg-primary {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-dark)
  ) !important;
}

.badge.bg-success {
  background: linear-gradient(135deg, var(--success-color), #059669) !important;
}

.badge.bg-warning {
  background: linear-gradient(135deg, var(--warning-color), #d97706) !important;
}

.badge.bg-info {
  background: linear-gradient(135deg, var(--info-color), #0891b2) !important;
}

/* File Type Icons */
.file-icon {
  font-size: 1.2em;
  margin-right: 0.5em;
}

.file-pdf {
  color: #dc3545;
}
.file-doc {
  color: #007bff;
}
.file-video {
  color: #28a745;
}
.file-image {
  color: #ffc107;
}
.file-archive {
  color: #6c757d;
}
.file-default {
  color: #6c757d;
}

/* Action Buttons */
.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  margin: 0 2px;
}

/* Dashboard Cards */
.dashboard-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 15px;
  padding: 20px;
  margin-bottom: 20px;
}

.dashboard-card h3 {
  font-size: 2.5rem;
  font-weight: bold;
  margin: 0;
}

.dashboard-card h5 {
  font-size: 1rem;
  opacity: 0.9;
  margin: 0;
}

/* Table Styles */
.table-responsive {
  border-radius: 10px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

.table tbody tr:hover {
  background-color: #f8f9fa;
}

/* Navigation */
.navbar {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-dark)
  ) !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
  border: none;
}

.navbar-brand {
  font-weight: 700;
  font-size: 1.5rem;
  color: white !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.navbar-brand i {
  background: linear-gradient(45deg, #fff, rgba(255, 255, 255, 0.8));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-right: 12px;
}

.dropdown-menu {
  border-radius: var(--border-radius);
  border: none;
  box-shadow: var(--card-shadow);
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.95);
}

.dropdown-item {
  padding: 12px 20px;
  transition: var(--transition);
  font-weight: 500;
}

.dropdown-item:hover {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-dark)
  );
  color: white;
  transform: translateX(5px);
}

/* Login Form */
#loginContainer {
  background: linear-gradient(
    135deg,
    rgba(99, 102, 241, 0.1),
    rgba(139, 92, 246, 0.1)
  );
  min-height: 100vh;
  display: flex;
  align-items: center;
}

#loginContainer .card {
  border-radius: 24px;
  overflow: hidden;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

#loginContainer .card-header {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-dark)
  );
  border: none;
  padding: 32px;
  text-align: center;
}

#loginContainer .card-header h4 {
  margin: 0;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

#loginContainer .card-body {
  padding: 40px;
}

/* Success/Error Messages */
.alert-success {
  background-color: #d4edda;
  border-color: #c3e6cb;
  color: #155724;
}

.alert-danger {
  background-color: #f8d7da;
  border-color: #f5c6cb;
  color: #721c24;
}

.alert-info {
  background-color: #d1ecf1;
  border-color: #bee5eb;
  color: #0c5460;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

/* Loading States */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.loading::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
