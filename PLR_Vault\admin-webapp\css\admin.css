/* Admin Panel Styles */
body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.sidebar {
    background-color: #343a40;
    min-height: calc(100vh - 56px);
    padding: 0;
}

.sidebar .list-group-item {
    background-color: transparent;
    border: none;
    color: #adb5bd;
    border-radius: 0;
    padding: 15px 20px;
    transition: all 0.3s ease;
}

.sidebar .list-group-item:hover {
    background-color: #495057;
    color: #fff;
}

.sidebar .list-group-item.active {
    background-color: #007bff;
    color: #fff;
    border-color: #007bff;
}

.main-content {
    padding: 30px;
}

.content-section {
    background-color: #fff;
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
}

.table {
    border-radius: 10px;
    overflow: hidden;
}

.table thead th {
    background-color: #007bff;
    color: #fff;
    border: none;
    font-weight: 600;
}

.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
    background-color: #007bff;
    color: #fff;
    border-radius: 15px 15px 0 0;
}

.form-control, .form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: border-color 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.alert {
    border-radius: 10px;
    border: none;
}

.progress {
    border-radius: 10px;
    height: 10px;
}

.progress-bar {
    border-radius: 10px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        min-height: auto;
    }
    
    .main-content {
        padding: 15px;
    }
    
    .content-section {
        padding: 20px;
    }
}

/* Loading Spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Status Badges */
.badge {
    font-size: 0.75em;
    padding: 0.5em 0.75em;
}

/* File Type Icons */
.file-icon {
    font-size: 1.2em;
    margin-right: 0.5em;
}

.file-pdf { color: #dc3545; }
.file-doc { color: #007bff; }
.file-video { color: #28a745; }
.file-image { color: #ffc107; }
.file-archive { color: #6c757d; }
.file-default { color: #6c757d; }

/* Action Buttons */
.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    margin: 0 2px;
}

/* Dashboard Cards */
.dashboard-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
}

.dashboard-card h3 {
    font-size: 2.5rem;
    font-weight: bold;
    margin: 0;
}

.dashboard-card h5 {
    font-size: 1rem;
    opacity: 0.9;
    margin: 0;
}

/* Table Styles */
.table-responsive {
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* Navigation */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.dropdown-menu {
    border-radius: 10px;
    border: none;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Login Form */
#loginContainer .card {
    border-radius: 20px;
    overflow: hidden;
}

#loginContainer .card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    padding: 20px;
}

/* Success/Error Messages */
.alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
