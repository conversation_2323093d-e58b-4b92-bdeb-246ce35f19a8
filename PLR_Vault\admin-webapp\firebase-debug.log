[debug] [2025-07-17T17:55:29.065Z] ----------------------------------------------------------------------
[debug] [2025-07-17T17:55:29.069Z] Command:       C:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\bin\firebase.js deploy --only hosting
[debug] [2025-07-17T17:55:29.070Z] CLI Version:   13.22.0
[debug] [2025-07-17T17:55:29.071Z] Platform:      win32
[debug] [2025-07-17T17:55:29.071Z] Node Version:  v23.5.0
[debug] [2025-07-17T17:55:29.071Z] Time:          Thu Jul 17 2025 22:55:29 GMT+0500 (Pakistan Standard Time)
[debug] [2025-07-17T17:55:29.071Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-07-17T17:55:29.156Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-17T17:55:29.157Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-07-17T17:55:29.157Z] [iam] checking project myapp-59f81 for permissions ["firebase.projects.get","firebasehosting.sites.update"]
[debug] [2025-07-17T17:55:29.158Z] Checked if tokens are valid: false, expires at: 1752775702813
[debug] [2025-07-17T17:55:29.159Z] Checked if tokens are valid: false, expires at: 1752775702813
[debug] [2025-07-17T17:55:29.159Z] > refreshing access token with scopes: []
[debug] [2025-07-17T17:55:29.163Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-07-17T17:55:29.163Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-07-17T17:55:29.672Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-07-17T17:55:29.672Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-07-17T17:55:29.694Z] >>> [apiv2][query] POST https://cloudresourcemanager.googleapis.com/v1/projects/myapp-59f81:testIamPermissions [none]
[debug] [2025-07-17T17:55:29.694Z] >>> [apiv2][(partial)header] POST https://cloudresourcemanager.googleapis.com/v1/projects/myapp-59f81:testIamPermissions x-goog-quota-user=projects/myapp-59f81
[debug] [2025-07-17T17:55:29.694Z] >>> [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/myapp-59f81:testIamPermissions {"permissions":["firebase.projects.get","firebasehosting.sites.update"]}
[debug] [2025-07-17T17:55:31.107Z] <<< [apiv2][status] POST https://cloudresourcemanager.googleapis.com/v1/projects/myapp-59f81:testIamPermissions 200
[debug] [2025-07-17T17:55:31.107Z] <<< [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/myapp-59f81:testIamPermissions {"permissions":["firebase.projects.get","firebasehosting.sites.update"]}
[debug] [2025-07-17T17:55:31.109Z] Checked if tokens are valid: true, expires at: 1752778528672
[debug] [2025-07-17T17:55:31.109Z] Checked if tokens are valid: true, expires at: 1752778528672
[debug] [2025-07-17T17:55:31.110Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/myapp-59f81 [none]
[debug] [2025-07-17T17:55:31.616Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/myapp-59f81 200
[debug] [2025-07-17T17:55:31.616Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/myapp-59f81 {"projectId":"myapp-59f81","projectNumber":"664484978086","displayName":"myapp","name":"projects/myapp-59f81","resources":{"hostingSite":"myapp-59f81","realtimeDatabaseInstance":"myapp-59f81-default-rtdb"},"state":"ACTIVE","etag":"1_a80e1357-3051-4ee5-8395-2fbf50fbb213"}
[info] 
[info] === Deploying to 'myapp-59f81'...
[info] 
[info] i  deploying hosting 
[debug] [2025-07-17T17:55:31.623Z] Checked if tokens are valid: true, expires at: 1752778528672
[debug] [2025-07-17T17:55:31.623Z] Checked if tokens are valid: true, expires at: 1752778528672
[debug] [2025-07-17T17:55:31.623Z] >>> [apiv2][query] POST https://firebasehosting.googleapis.com/v1beta1/projects/-/sites/myapp-59f81/versions [none]
[debug] [2025-07-17T17:55:31.624Z] >>> [apiv2][body] POST https://firebasehosting.googleapis.com/v1beta1/projects/-/sites/myapp-59f81/versions {"status":"CREATED","labels":{"deployment-tool":"cli-firebase"}}
