[debug] [2025-07-17T17:52:59.070Z] ----------------------------------------------------------------------
[debug] [2025-07-17T17:52:59.073Z] Command:       C:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\bin\firebase.js deploy --only hosting
[debug] [2025-07-17T17:52:59.074Z] CLI Version:   13.22.0
[debug] [2025-07-17T17:52:59.074Z] Platform:      win32
[debug] [2025-07-17T17:52:59.074Z] Node Version:  v23.5.0
[debug] [2025-07-17T17:52:59.074Z] Time:          Thu Jul 17 2025 22:52:59 GMT+0500 (Pakistan Standard Time)
[debug] [2025-07-17T17:52:59.074Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-07-17T17:52:59.187Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-17T17:52:59.188Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-07-17T17:52:59.188Z] [iam] checking project myapp-59f81 for permissions ["firebase.projects.get","firebasehosting.sites.update"]
[debug] [2025-07-17T17:52:59.189Z] Checked if tokens are valid: true, expires at: 1752775702813
[debug] [2025-07-17T17:52:59.189Z] Checked if tokens are valid: true, expires at: 1752775702813
[debug] [2025-07-17T17:52:59.190Z] >>> [apiv2][query] POST https://cloudresourcemanager.googleapis.com/v1/projects/myapp-59f81:testIamPermissions [none]
[debug] [2025-07-17T17:52:59.191Z] >>> [apiv2][(partial)header] POST https://cloudresourcemanager.googleapis.com/v1/projects/myapp-59f81:testIamPermissions x-goog-quota-user=projects/myapp-59f81
[debug] [2025-07-17T17:52:59.191Z] >>> [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/myapp-59f81:testIamPermissions {"permissions":["firebase.projects.get","firebasehosting.sites.update"]}
[debug] [2025-07-17T17:53:00.765Z] <<< [apiv2][status] POST https://cloudresourcemanager.googleapis.com/v1/projects/myapp-59f81:testIamPermissions 200
[debug] [2025-07-17T17:53:00.765Z] <<< [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/myapp-59f81:testIamPermissions {"permissions":["firebase.projects.get","firebasehosting.sites.update"]}
[debug] [2025-07-17T17:53:00.766Z] Checked if tokens are valid: true, expires at: 1752775702813
[debug] [2025-07-17T17:53:00.766Z] Checked if tokens are valid: true, expires at: 1752775702813
[debug] [2025-07-17T17:53:00.766Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/myapp-59f81 [none]
[debug] [2025-07-17T17:53:01.291Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/myapp-59f81 200
[debug] [2025-07-17T17:53:01.292Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/myapp-59f81 {"projectId":"myapp-59f81","projectNumber":"664484978086","displayName":"myapp","name":"projects/myapp-59f81","resources":{"hostingSite":"myapp-59f81","realtimeDatabaseInstance":"myapp-59f81-default-rtdb"},"state":"ACTIVE","etag":"1_a80e1357-3051-4ee5-8395-2fbf50fbb213"}
[info] 
[info] === Deploying to 'myapp-59f81'...
[info] 
[info] i  deploying hosting 
[debug] [2025-07-17T17:53:01.307Z] Checked if tokens are valid: true, expires at: 1752775702813
[debug] [2025-07-17T17:53:01.308Z] Checked if tokens are valid: true, expires at: 1752775702813
[debug] [2025-07-17T17:53:01.308Z] >>> [apiv2][query] POST https://firebasehosting.googleapis.com/v1beta1/projects/-/sites/myapp-59f81/versions [none]
[debug] [2025-07-17T17:53:01.309Z] >>> [apiv2][body] POST https://firebasehosting.googleapis.com/v1beta1/projects/-/sites/myapp-59f81/versions {"status":"CREATED","labels":{"deployment-tool":"cli-firebase"}}
[debug] [2025-07-17T17:53:03.764Z] <<< [apiv2][status] POST https://firebasehosting.googleapis.com/v1beta1/projects/-/sites/myapp-59f81/versions 200
[debug] [2025-07-17T17:53:03.765Z] <<< [apiv2][body] POST https://firebasehosting.googleapis.com/v1beta1/projects/-/sites/myapp-59f81/versions {"name":"projects/664484978086/sites/myapp-59f81/versions/f51677fbf236ddf0","status":"CREATED","config":{},"labels":{"deployment-tool":"cli-firebase"}}
[info] i  hosting[myapp-59f81]: beginning deploy... 
[info] i  hosting[myapp-59f81]: found 5 files in . 
[debug] [2025-07-17T17:53:03.800Z] [hosting] uploading with 200 concurrency
[debug] [2025-07-17T17:53:03.813Z] Checked if tokens are valid: true, expires at: 1752775702813
[debug] [2025-07-17T17:53:03.813Z] Checked if tokens are valid: true, expires at: 1752775702813
[debug] [2025-07-17T17:53:03.813Z] [hosting] hash cache [] stored for 5 files
[debug] [2025-07-17T17:53:03.814Z] [hosting][hash queue][FINAL] {"max":8,"min":2,"avg":4.4,"active":0,"complete":5,"success":5,"errored":0,"retried":0,"total":5,"elapsed":11}
[debug] [2025-07-17T17:53:03.814Z] >>> [apiv2][query] POST https://firebasehosting.googleapis.com/v1beta1/projects/664484978086/sites/myapp-59f81/versions/f51677fbf236ddf0:populateFiles [none]
[debug] [2025-07-17T17:53:03.815Z] >>> [apiv2][body] POST https://firebasehosting.googleapis.com/v1beta1/projects/664484978086/sites/myapp-59f81/versions/f51677fbf236ddf0:populateFiles {"files":{"/README.md":"0433c62019f5674155b45508b6cd637f9be3cb47e7608bb021eafb1cc805badc","/js/firebase-config.js":"7cf68e588edd13734f56c620e6ced7dd12eda36d7e7427dab02789caf5b57bf4","/css/admin.css":"93889957d46aa9c7569d0ff2da7dc8b69b53b70671339ee5b1788c51c12d31dc","/index.html":"d4d3c3427dde70986f4804bcd2675f63021fd33f6ade97696481e11c73fb6f41","/js/admin.js":"b3c655734aec5888e009eba5eb932a15fe94c40d0c4bf0b2ba411faf37de38bc"}}
[debug] [2025-07-17T17:53:04.411Z] <<< [apiv2][status] POST https://firebasehosting.googleapis.com/v1beta1/projects/664484978086/sites/myapp-59f81/versions/f51677fbf236ddf0:populateFiles 200
[debug] [2025-07-17T17:53:04.411Z] <<< [apiv2][body] POST https://firebasehosting.googleapis.com/v1beta1/projects/664484978086/sites/myapp-59f81/versions/f51677fbf236ddf0:populateFiles {"uploadRequiredHashes":["d4d3c3427dde70986f4804bcd2675f63021fd33f6ade97696481e11c73fb6f41","b3c655734aec5888e009eba5eb932a15fe94c40d0c4bf0b2ba411faf37de38bc"],"uploadUrl":"https://upload-firebasehosting.googleapis.com/upload/sites/myapp-59f81/versions/f51677fbf236ddf0/files"}
[debug] [2025-07-17T17:53:04.412Z] Checked if tokens are valid: true, expires at: 1752775702813
[debug] [2025-07-17T17:53:04.412Z] Checked if tokens are valid: true, expires at: 1752775702813
[debug] [2025-07-17T17:53:04.412Z] Checked if tokens are valid: true, expires at: 1752775702813
[debug] [2025-07-17T17:53:04.412Z] Checked if tokens are valid: true, expires at: 1752775702813
[debug] [2025-07-17T17:53:04.413Z] >>> [apiv2][query] POST https://upload-firebasehosting.googleapis.com/upload/sites/myapp-59f81/versions/f51677fbf236ddf0/files/d4d3c3427dde70986f4804bcd2675f63021fd33f6ade97696481e11c73fb6f41 [none]
[debug] [2025-07-17T17:53:04.413Z] >>> [apiv2][body] POST https://upload-firebasehosting.googleapis.com/upload/sites/myapp-59f81/versions/f51677fbf236ddf0/files/d4d3c3427dde70986f4804bcd2675f63021fd33f6ade97696481e11c73fb6f41 [stream]
[debug] [2025-07-17T17:53:04.414Z] >>> [apiv2][query] POST https://upload-firebasehosting.googleapis.com/upload/sites/myapp-59f81/versions/f51677fbf236ddf0/files/b3c655734aec5888e009eba5eb932a15fe94c40d0c4bf0b2ba411faf37de38bc [none]
[debug] [2025-07-17T17:53:04.414Z] >>> [apiv2][body] POST https://upload-firebasehosting.googleapis.com/upload/sites/myapp-59f81/versions/f51677fbf236ddf0/files/b3c655734aec5888e009eba5eb932a15fe94c40d0c4bf0b2ba411faf37de38bc [stream]
[debug] [2025-07-17T17:53:04.416Z] [hosting][populate queue][FINAL] {"max":600,"min":600,"avg":600,"active":0,"complete":1,"success":1,"errored":0,"retried":0,"total":1,"elapsed":604}
[debug] [2025-07-17T17:53:04.416Z] [hosting] uploads queued: 2
[debug] [2025-07-17T17:53:06.067Z] <<< [apiv2][status] POST https://upload-firebasehosting.googleapis.com/upload/sites/myapp-59f81/versions/f51677fbf236ddf0/files/d4d3c3427dde70986f4804bcd2675f63021fd33f6ade97696481e11c73fb6f41 200
[debug] [2025-07-17T17:53:06.067Z] <<< [apiv2][body] POST https://upload-firebasehosting.googleapis.com/upload/sites/myapp-59f81/versions/f51677fbf236ddf0/files/d4d3c3427dde70986f4804bcd2675f63021fd33f6ade97696481e11c73fb6f41 [stream]
[debug] [2025-07-17T17:53:06.880Z] <<< [apiv2][status] POST https://upload-firebasehosting.googleapis.com/upload/sites/myapp-59f81/versions/f51677fbf236ddf0/files/b3c655734aec5888e009eba5eb932a15fe94c40d0c4bf0b2ba411faf37de38bc 200
[debug] [2025-07-17T17:53:06.908Z] <<< [apiv2][body] POST https://upload-firebasehosting.googleapis.com/upload/sites/myapp-59f81/versions/f51677fbf236ddf0/files/b3c655734aec5888e009eba5eb932a15fe94c40d0c4bf0b2ba411faf37de38bc [stream]
[debug] [2025-07-17T17:53:06.932Z] [hosting][upload queue][FINAL] {"max":2497,"min":1655,"avg":2076,"active":0,"complete":2,"success":2,"errored":0,"retried":0,"total":2,"elapsed":2521}
[info] +  hosting[myapp-59f81]: file upload complete 
[debug] [2025-07-17T17:53:06.984Z] [hosting] deploy completed after 3210ms
[debug] [2025-07-17T17:53:06.984Z] [
  {
    "config": {
      "public": ".",
      "ignore": [
        "firebase.json",
        "**/.*",
        "**/node_modules/**"
      ],
      "rewrites": [
        {
          "source": "**",
          "destination": "/index.html"
        }
      ],
      "headers": [
        {
          "source": "**/*.@(js|css)",
          "headers": [
            {
              "key": "Cache-Control",
              "value": "max-age=31536000"
            }
          ]
        }
      ],
      "site": "myapp-59f81"
    },
    "version": "projects/664484978086/sites/myapp-59f81/versions/f51677fbf236ddf0"
  }
]
[info] i  hosting[myapp-59f81]: finalizing version... 
[debug] [2025-07-17T17:53:06.994Z] Checked if tokens are valid: true, expires at: 1752775702813
[debug] [2025-07-17T17:53:06.994Z] Checked if tokens are valid: true, expires at: 1752775702813
[debug] [2025-07-17T17:53:06.995Z] >>> [apiv2][query] PATCH https://firebasehosting.googleapis.com/v1beta1/projects/-/sites/myapp-59f81/versions/f51677fbf236ddf0 updateMask=status%2Cconfig
[debug] [2025-07-17T17:53:06.995Z] >>> [apiv2][body] PATCH https://firebasehosting.googleapis.com/v1beta1/projects/-/sites/myapp-59f81/versions/f51677fbf236ddf0 {"status":"FINALIZED","config":{"rewrites":[{"glob":"**","path":"/index.html"}],"headers":[{"glob":"**/*.@(js|css)","headers":{"Cache-Control":"max-age=31536000"}}]}}
