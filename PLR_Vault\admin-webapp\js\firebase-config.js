// Firebase configuration
import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
import { getAuth } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js";
import { getDatabase } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-database.js";
import { getStorage } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-storage.js";

// Your web app's Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyDtuFLIDJzL9w8a5b1KqF1SqHSdkOHq09Q",
  authDomain: "myapp-59f81.firebaseapp.com",
  databaseURL: "https://myapp-59f81-default-rtdb.firebaseio.com",
  projectId: "myapp-59f81",
  storageBucket: "myapp-59f81.firebasestorage.app",
  messagingSenderId: "664484978086",
  appId: "1:664484978086:web:9f7309b730655cb431c367",
  measurementId: "G-L62ZTJG5JK"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase services
export const auth = getAuth(app);
export const database = getDatabase(app);
export const storage = getStorage(app);

// Export the app instance
export default app;
