<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta
      http-equiv="Cache-Control"
      content="no-cache, no-store, must-revalidate"
    />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Expires" content="0" />
    <title>PLR Vault Admin Panel</title>
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
      rel="stylesheet"
    />
    <link href="css/admin.css?v=2024011701" rel="stylesheet" />
  </head>
  <body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
      <div class="container-fluid">
        <a class="navbar-brand" href="#">
          <i class="fas fa-shield-alt me-2"></i>
          PLR Vault Admin
        </a>
        <div class="navbar-nav ms-auto">
          <div
            class="nav-item dropdown"
            id="userDropdown"
            style="display: none"
          >
            <a
              class="nav-link dropdown-toggle"
              href="#"
              role="button"
              data-bs-toggle="dropdown"
            >
              <i class="fas fa-user-circle me-1"></i>
              <span id="userEmail"></span>
            </a>
            <ul class="dropdown-menu">
              <li>
                <a class="dropdown-item" href="#" onclick="logout()">
                  <i class="fas fa-sign-out-alt me-2"></i>Logout
                </a>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </nav>

    <!-- Login Form -->
    <div id="loginContainer" class="container mt-5">
      <div class="row justify-content-center">
        <div class="col-md-6">
          <div class="card shadow">
            <div class="card-header bg-primary text-white text-center">
              <h4><i class="fas fa-lock me-2"></i>Admin Login</h4>
            </div>
            <div class="card-body">
              <form id="loginForm">
                <div class="mb-3">
                  <label for="email" class="form-label">Email</label>
                  <input
                    type="email"
                    class="form-control"
                    id="email"
                    required
                  />
                </div>
                <div class="mb-3">
                  <label for="password" class="form-label">Password</label>
                  <input
                    type="password"
                    class="form-control"
                    id="password"
                    required
                  />
                </div>
                <div class="d-grid">
                  <button type="submit" class="btn btn-primary">
                    <i class="fas fa-sign-in-alt me-2"></i>Login with Email
                  </button>
                </div>
              </form>

              <div class="text-center my-3">
                <span class="text-muted">or</span>
              </div>

              <div class="d-grid">
                <button
                  type="button"
                  class="btn btn-outline-danger"
                  onclick="signInWithGoogle()"
                >
                  <i class="fab fa-google me-2"></i>Continue with Google
                </button>
              </div>

              <div
                id="loginError"
                class="alert alert-danger mt-3"
                style="display: none"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Admin Dashboard -->
    <div id="adminDashboard" style="display: none">
      <div class="container-fluid">
        <div class="row">
          <!-- Sidebar -->
          <div class="col-md-3 col-lg-2 sidebar">
            <div class="list-group list-group-flush">
              <a
                href="#"
                class="list-group-item list-group-item-action active"
                onclick="showSection('dashboard')"
              >
                <i class="fas fa-tachometer-alt me-2"></i>Dashboard
              </a>
              <a
                href="#"
                class="list-group-item list-group-item-action"
                onclick="showSection('content')"
              >
                <i class="fas fa-file-alt me-2"></i>Manage Content
              </a>
              <a
                href="#"
                class="list-group-item list-group-item-action"
                onclick="showSection('users')"
                id="usersLink"
                style="display: none"
              >
                <i class="fas fa-users me-2"></i>Manage Users
              </a>
              <a
                href="#"
                class="list-group-item list-group-item-action"
                onclick="showSection('admins')"
                id="adminsLink"
                style="display: none"
              >
                <i class="fas fa-user-shield me-2"></i>Manage Admins
              </a>
            </div>
          </div>

          <!-- Main Content -->
          <div class="col-md-9 col-lg-10 main-content">
            <!-- Dashboard Section -->
            <div id="dashboardSection" class="content-section">
              <h2><i class="fas fa-tachometer-alt me-2"></i>Dashboard</h2>
              <div class="row g-4">
                <div class="col-md-3">
                  <div class="card bg-primary text-white">
                    <div class="card-body">
                      <div
                        class="d-flex justify-content-between align-items-center"
                      >
                        <div>
                          <h5 class="mb-2 opacity-75">Total Content</h5>
                          <h2 id="totalContent" class="mb-0 fw-bold">0</h2>
                        </div>
                        <div class="align-self-center">
                          <i class="fas fa-file-alt fa-3x opacity-75"></i>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="card bg-success text-white">
                    <div class="card-body">
                      <div
                        class="d-flex justify-content-between align-items-center"
                      >
                        <div>
                          <h5 class="mb-2 opacity-75">Total Users</h5>
                          <h2 id="totalUsers" class="mb-0 fw-bold">0</h2>
                        </div>
                        <div class="align-self-center">
                          <i class="fas fa-users fa-3x opacity-75"></i>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="card bg-warning text-white">
                    <div class="card-body">
                      <div
                        class="d-flex justify-content-between align-items-center"
                      >
                        <div>
                          <h5 class="mb-2 opacity-75">Total Admins</h5>
                          <h2 id="totalAdmins" class="mb-0 fw-bold">0</h2>
                        </div>
                        <div class="align-self-center">
                          <i class="fas fa-user-shield fa-3x opacity-75"></i>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="card bg-info text-white">
                    <div class="card-body">
                      <div
                        class="d-flex justify-content-between align-items-center"
                      >
                        <div>
                          <h5 class="mb-2 opacity-75">Categories</h5>
                          <h2 id="totalCategories" class="mb-0 fw-bold">20</h2>
                        </div>
                        <div class="align-self-center">
                          <i class="fas fa-tags fa-3x opacity-75"></i>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Content Management Section -->
            <div
              id="contentSection"
              class="content-section"
              style="display: none"
            >
              <div
                class="d-flex justify-content-between align-items-center mb-4"
              >
                <h2><i class="fas fa-file-alt me-2"></i>Manage Content</h2>
                <div>
                  <button
                    class="btn btn-warning me-2"
                    onclick="fixAllCategories()"
                    title="Fix all content categories based on file types and content"
                  >
                    <i class="fas fa-magic me-2"></i>Fix Categories
                  </button>
                  <button
                    class="btn btn-primary"
                    onclick="showAddContentModal()"
                  >
                    <i class="fas fa-plus me-2"></i>Add Content
                  </button>
                </div>
              </div>

              <!-- Category Filter -->
              <div class="mb-3">
                <select
                  class="form-select"
                  id="categoryFilter"
                  onchange="filterContentByCategory()"
                >
                  <option value="">All Categories</option>
                </select>
              </div>

              <!-- Content Table -->
              <div class="table-responsive">
                <table class="table table-striped">
                  <thead>
                    <tr>
                      <th>Title</th>
                      <th>Category</th>
                      <th>File Type</th>
                      <th>Size</th>
                      <th>Created</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody id="contentTableBody"></tbody>
                </table>
              </div>
            </div>

            <!-- Users Management Section -->
            <div
              id="usersSection"
              class="content-section"
              style="display: none"
            >
              <h2><i class="fas fa-users me-2"></i>Manage Users</h2>
              <div class="table-responsive">
                <table class="table table-striped">
                  <thead>
                    <tr>
                      <th>Email</th>
                      <th>Display Name</th>
                      <th>Role</th>
                      <th>Sign Up Method</th>
                      <th>Created</th>
                      <th>Last Login</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody id="usersTableBody"></tbody>
                </table>
              </div>
            </div>

            <!-- Admins Management Section -->
            <div
              id="adminsSection"
              class="content-section"
              style="display: none"
            >
              <div
                class="d-flex justify-content-between align-items-center mb-4"
              >
                <h2><i class="fas fa-user-shield me-2"></i>Manage Admins</h2>
                <button class="btn btn-primary" onclick="showAddAdminModal()">
                  <i class="fas fa-plus me-2"></i>Add Admin
                </button>
              </div>
              <div class="table-responsive">
                <table class="table table-striped">
                  <thead>
                    <tr>
                      <th>Email</th>
                      <th>Display Name</th>
                      <th>Created</th>
                      <th>Last Login</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody id="adminsTableBody"></tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Add Content Modal -->
    <div class="modal fade" id="addContentModal" tabindex="-1">
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Add New Content</h5>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
            ></button>
          </div>
          <div class="modal-body">
            <form id="addContentForm">
              <div class="mb-3">
                <label for="contentTitle" class="form-label">Title</label>
                <input
                  type="text"
                  class="form-control"
                  id="contentTitle"
                  required
                />
              </div>
              <div class="mb-3">
                <label for="contentDescription" class="form-label"
                  >Description</label
                >
                <textarea
                  class="form-control"
                  id="contentDescription"
                  rows="3"
                  required
                ></textarea>
              </div>
              <div class="mb-3">
                <label for="contentCategory" class="form-label">Category</label>
                <select
                  class="form-select"
                  id="contentCategory"
                  required
                ></select>
              </div>
              <div class="mb-3">
                <label for="contentFile" class="form-label">File</label>
                <input
                  type="file"
                  class="form-control"
                  id="contentFile"
                  required
                />
              </div>
              <div
                class="progress mb-3"
                id="uploadProgress"
                style="display: none"
              >
                <div
                  class="progress-bar"
                  role="progressbar"
                  style="width: 0%"
                ></div>
              </div>
            </form>
          </div>
          <div class="modal-footer">
            <button
              type="button"
              class="btn btn-secondary"
              data-bs-dismiss="modal"
            >
              Cancel
            </button>
            <button
              type="button"
              class="btn btn-primary"
              onclick="addContent()"
            >
              Add Content
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Add Admin Modal -->
    <div class="modal fade" id="addAdminModal" tabindex="-1">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Add New Admin</h5>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
            ></button>
          </div>
          <div class="modal-body">
            <form id="addAdminForm">
              <div class="mb-3">
                <label for="adminEmail" class="form-label">Email</label>
                <input
                  type="email"
                  class="form-control"
                  id="adminEmail"
                  required
                />
              </div>
              <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                The user must already be registered in the app to be made an
                admin.
              </div>
            </form>
          </div>
          <div class="modal-footer">
            <button
              type="button"
              class="btn btn-secondary"
              data-bs-dismiss="modal"
            >
              Cancel
            </button>
            <button type="button" class="btn btn-primary" onclick="addAdmin()">
              Add Admin
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Edit Content Modal -->
    <div class="modal fade" id="editContentModal" tabindex="-1">
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Edit Content</h5>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
            ></button>
          </div>
          <div class="modal-body">
            <form id="editContentForm">
              <input type="hidden" id="editContentId" />
              <div class="mb-3">
                <label for="editContentTitle" class="form-label">Title</label>
                <input
                  type="text"
                  class="form-control"
                  id="editContentTitle"
                  required
                />
              </div>
              <div class="mb-3">
                <label for="editContentDescription" class="form-label"
                  >Description</label
                >
                <textarea
                  class="form-control"
                  id="editContentDescription"
                  rows="3"
                  required
                ></textarea>
              </div>
              <div class="mb-3">
                <label for="editContentCategory" class="form-label"
                  >Category</label
                >
                <select
                  class="form-select"
                  id="editContentCategory"
                  required
                ></select>
              </div>
              <div class="mb-3">
                <label class="form-label">Current File</label>
                <div class="alert alert-info">
                  <i class="fas fa-file me-2"></i>
                  <span id="currentFileName">No file</span>
                </div>
              </div>
              <div class="mb-3">
                <label for="editContentFile" class="form-label"
                  >Replace File (Optional)</label
                >
                <input
                  type="file"
                  class="form-control"
                  id="editContentFile"
                  accept="*/*"
                />
                <div class="form-text">
                  Leave empty to keep the current file
                </div>
              </div>
              <div id="editUploadProgress" style="display: none">
                <div class="progress mb-3">
                  <div class="progress-bar" style="width: 0%"></div>
                </div>
              </div>
            </form>
          </div>
          <div class="modal-footer">
            <button
              type="button"
              class="btn btn-secondary"
              data-bs-dismiss="modal"
            >
              Cancel
            </button>
            <button
              type="button"
              class="btn btn-primary"
              onclick="updateContent()"
            >
              Update Content
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script type="module" src="js/firebase-config.js"></script>
    <script type="module" src="js/admin.js?v=2024071729"></script>
  </body>
</html>
